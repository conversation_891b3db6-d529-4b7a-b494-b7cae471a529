import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
  InternalServerErrorException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CrawlerService } from './crawler.service';
import { PlaywrightCrawlerService } from './playwright-crawler.service';
import {
  CrawlRequestDto,
  CrawlResponseDto,
  PlaywrightCrawlRequestDto,
  PlaywrightCrawlResponseDto,
} from './dto';

@Controller('crawl')
export class CrawlerController {
  private readonly logger = new Logger(CrawlerController.name);

  constructor(
    private readonly crawlerService: CrawlerService,
    private readonly playwrightCrawlerService: PlaywrightCrawlerService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async crawlUrl(
    @Body() crawlRequest: CrawlRequestDto,
  ): Promise<CrawlResponseDto> {
    this.logger.log(`Received crawl request for URL: ${crawlRequest.url}`);

    try {
      // Validate URL format
      if (!this.isValidUrl(crawlRequest.url)) {
        throw new BadRequestException('Invalid URL format provided');
      }

      // Start crawling
      const result = await this.crawlerService.crawlSingleUrl(
        crawlRequest.url,
        crawlRequest.maxConcurrency,
        crawlRequest.maxPages,
      );

      this.logger.log(
        `Crawl completed successfully for URL: ${crawlRequest.url}`,
      );
      return CrawlResponseDto.success(
        'Crawling completed successfully',
        result.data,
        result.crawlId,
      );
    } catch (error) {
      const errorMsg =
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred during crawling';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Crawl failed for URL: ${crawlRequest.url}`,
        errorStack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle network errors, timeout errors, etc.
      throw new InternalServerErrorException(
        CrawlResponseDto.error(
          'Crawling failed',
          errorMsg ?? 'An unexpected error occurred during crawling',
        ),
      );
    }
  }

  @Post('playwright')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async crawlUrlWithPlaywright(
    @Body() crawlRequest: PlaywrightCrawlRequestDto,
  ): Promise<PlaywrightCrawlResponseDto> {
    this.logger.log(
      `Received Playwright crawl request for URL: ${crawlRequest.url}`,
    );

    try {
      // Validate URL format
      if (!this.isValidUrl(crawlRequest.url)) {
        throw new BadRequestException('Invalid URL format provided');
      }

      const startTime = Date.now();

      // Start Playwright crawling
      const result = await this.playwrightCrawlerService.crawlSingleUrl(
        crawlRequest.url,
        crawlRequest.maxConcurrency,
        crawlRequest.maxPages,
        {
          headless: crawlRequest.headless,
          browserType: crawlRequest.browserType,
          waitForSelector: crawlRequest.waitForSelector,
          waitForTimeout: crawlRequest.waitForTimeout,
          screenshot: crawlRequest.screenshot,
          extractJavaScript: crawlRequest.extractJavaScript,
          viewport: crawlRequest.viewport,
          userAgent: crawlRequest.userAgent,
          blockImages: crawlRequest.blockImages,
          blockCSS: crawlRequest.blockCSS,
          extraHeaders: crawlRequest.extraHeaders,
          proxy: crawlRequest.proxy,
        },
      );

      const duration = Date.now() - startTime;

      this.logger.log(
        `Playwright crawl completed successfully for URL: ${crawlRequest.url}`,
      );

      return PlaywrightCrawlResponseDto.success(
        'Playwright crawling completed successfully',
        result.data,
        result.crawlId,
        result.screenshots,
        duration,
        crawlRequest.browserType,
        crawlRequest.headless,
      );
    } catch (error) {
      const errorMsg =
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred during Playwright crawling';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Playwright crawl failed for URL: ${crawlRequest.url}`,
        errorStack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle network errors, timeout errors, etc.
      throw new InternalServerErrorException(
        PlaywrightCrawlResponseDto.error(
          'Playwright crawling failed',
          errorMsg ?? 'An unexpected error occurred during Playwright crawling',
        ),
      );
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }
}
