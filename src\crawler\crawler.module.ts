import { Module } from '@nestjs/common';
import { CrawlerService } from './crawler.service';
import { PlaywrightCrawlerService } from './playwright-crawler.service';
import { CrawlerController } from './crawler.controller';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [ConfigModule, ScheduleModule.forRoot()],
  controllers: [CrawlerController],
  providers: [CrawlerService, PlaywrightCrawlerService],
  exports: [CrawlerService, PlaywrightCrawlerService],
})
export class CrawlerModule {}
