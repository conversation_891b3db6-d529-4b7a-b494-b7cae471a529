# Multi-stage Dockerfile for development and production

# Base stage with common dependencies
FROM node:20-alpine AS base

# Install system dependencies including browser automation requirements
RUN apk add --no-cache \
    ca-certificates \
    chromium \
    curl \
    freetype \
    freetype-dev \
    git \
    harfbuzz \
    nss \
    ttf-freefont \
    wget \
    && rm -rf /var/cache/apk/*

# Set environment variables for Playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
ENV PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH=/usr/bin/chromium-browser

WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./

# Development stage
FROM base AS development

# Install all dependencies (including dev dependencies) and Playwright browsers
RUN npm ci && \
    npx playwright install chromium

# Copy source code
COPY . .

# Create storage directories with proper permissions
RUN mkdir -p storage/screenshots && chown -R node:node storage

# Switch to non-root user
USER node

# Expose application port and debug port
EXPOSE 3000 9229

# Default command for development (can be overridden)
CMD ["npm", "run", "start:dev"]

