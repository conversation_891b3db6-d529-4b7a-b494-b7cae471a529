import { BrowserType, PlaywrightCrawledData } from '../interfaces/playwright.interfaces';

export class PlaywrightCrawlResponseDto {
  success: boolean;
  message: string;
  data?: PlaywrightCrawledData[];
  error?: string;
  crawlId?: string;
  totalPages?: number;
  screenshots?: string[];
  duration?: number;
  browserType?: BrowserType;
  headless?: boolean;
  startedAt: Date;
  completedAt?: Date;

  constructor(
    success: boolean,
    message: string,
    data?: PlaywrightCrawledData[],
    error?: string,
    crawlId?: string,
    screenshots?: string[],
    duration?: number,
    browserType?: BrowserType,
    headless?: boolean,
  ) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.error = error;
    this.crawlId = crawlId;
    this.screenshots = screenshots;
    this.duration = duration;
    this.browserType = browserType;
    this.headless = headless;
    this.startedAt = new Date();
    this.totalPages = data?.length || 0;
  }

  static success(
    message: string,
    data: PlaywrightCrawledData[],
    crawlId?: string,
    screenshots?: string[],
    duration?: number,
    browserType?: BrowserType,
    headless?: boolean,
  ): PlaywrightCrawlResponseDto {
    const response = new PlaywrightCrawlResponseDto(
      true,
      message,
      data,
      undefined,
      crawlId,
      screenshots,
      duration,
      browserType,
      headless,
    );
    response.completedAt = new Date();
    return response;
  }

  static error(message: string, error: string): PlaywrightCrawlResponseDto {
    return new PlaywrightCrawlResponseDto(
      false,
      message,
      undefined,
      error,
    );
  }
}
