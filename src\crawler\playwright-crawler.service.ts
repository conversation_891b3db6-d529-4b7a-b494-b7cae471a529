import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PlaywrightCrawler, Dataset } from '@crawlee/playwright';
import { PlaywrightCrawledData } from './interfaces/playwright.interfaces';

export interface PlaywrightCrawlOptions {
  headless?: boolean;
  browserType?: 'chromium' | 'firefox' | 'webkit';
  waitForSelector?: string;
  waitForTimeout?: number;
  screenshot?: boolean;
  extractJavaScript?: boolean;
}

export interface PlaywrightCrawlResult {
  data: PlaywrightCrawledData[];
  crawlId: string;
  totalPages: number;
  screenshots?: string[];
}

@Injectable()
export class PlaywrightCrawlerService {
  private readonly logger = new Logger(PlaywrightCrawlerService.name);

  constructor(private readonly config: ConfigService) {}

  /**
   * Crawl a single URL using Playwright for JavaScript-heavy sites
   */
  async crawlSingleUrl(
    url: string,
    maxConcurrency: number = 3,
    maxPages: number = 10,
    options: PlaywrightCrawlOptions = {},
  ): Promise<PlaywrightCrawlResult> {
    this.validateUrl(url);

    const crawlId = this.generateCrawlId();
    this.logger.log(
      `Starting Playwright crawl for URL: ${url} (ID: ${crawlId})`,
    );

    const crawledData: PlaywrightCrawledData[] = [];
    const screenshots: string[] = [];
    let pageCount = 0;

    // Set default options
    const crawlOptions = {
      headless: options.headless ?? true,
      browserType: options.browserType ?? 'chromium',
      waitForTimeout: options.waitForTimeout ?? 5000,
      screenshot: options.screenshot ?? false,
      extractJavaScript: options.extractJavaScript ?? false,
      ...options,
    };

    const crawler = new PlaywrightCrawler({
      requestHandler: async ({ page, request, enqueueLinks }) => {
        if (pageCount >= maxPages) {
          this.logger.log(
            `Reached maximum page limit (${maxPages}) for crawl ${crawlId}`,
          );
          return;
        }

        this.logger.log(
          `Crawling ${request.url} with Playwright (${pageCount + 1}/${maxPages})`,
        );

        const pageStartTime = Date.now();

        try {
          // Wait for specific selector if provided
          if (crawlOptions.waitForSelector) {
            await page.waitForSelector(crawlOptions.waitForSelector, {
              timeout: crawlOptions.waitForTimeout,
            });
          } else {
            // Wait for page to load
            await page.waitForLoadState('networkidle', {
              timeout: crawlOptions.waitForTimeout,
            });
          }

          // Extract data using Playwright's page API
          const title = (await page.title()) || 'No title found';

          // Extract additional data that might be loaded via JavaScript
          const additionalData: any = {};

          if (crawlOptions.extractJavaScript) {
            // Extract data that might be dynamically loaded
            additionalData.metaDescription = await page
              .locator('meta[name="description"]')
              .getAttribute('content')
              .catch(() => null);

            additionalData.headings = await page
              .locator('h1, h2, h3')
              .allTextContents()
              .catch(() => []);
          }

          const crawledItem: PlaywrightCrawledData = {
            url: request.url,
            title,
            crawledAt: new Date(),
            metaDescription: additionalData.metaDescription,
            headings: additionalData.headings,
            loadTime: Date.now() - pageStartTime,
            statusCode: page.url() ? 200 : undefined,
          };

          crawledData.push(crawledItem);
          pageCount++;

          // Take screenshot if requested
          if (crawlOptions.screenshot) {
            const screenshotPath = `storage/screenshots/${crawlId}_${pageCount}.png`;
            await page.screenshot({
              path: screenshotPath,
              fullPage: true,
            });
            screenshots.push(screenshotPath);
          }

          // Save to dataset for persistence
          await Dataset.pushData(crawledItem);

          // Enqueue further links only if we haven't reached the limit
          if (pageCount < maxPages) {
            await enqueueLinks({
              selector: 'a[href]',
              limit: Math.min(3, maxPages - pageCount), // More conservative for Playwright
            });
          }
        } catch (error) {
          this.logger.error(
            `Error processing ${request.url} with Playwright:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      },
      maxConcurrency,
      maxRequestsPerCrawl: maxPages,
      requestHandlerTimeoutSecs: 60, // Longer timeout for Playwright
      launchContext: {
        launchOptions: {
          headless: crawlOptions.headless,
        },
      },
    });

    try {
      await crawler.run([url]);
      this.logger.log(
        `Playwright crawl completed for ${url} (ID: ${crawlId}). Pages crawled: ${crawledData.length}`,
      );

      return {
        data: crawledData,
        crawlId,
        totalPages: crawledData.length,
        screenshots: screenshots.length > 0 ? screenshots : undefined,
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Playwright crawl failed for ${url} (ID: ${crawlId}):`,
        errorMsg,
      );
      throw new Error(`Playwright crawling failed: ${errorMsg}`);
    }
  }

  /**
   * Validate URL format and accessibility
   */
  private validateUrl(url: string): void {
    if (!url || typeof url !== 'string') {
      throw new BadRequestException('URL is required and must be a string');
    }

    try {
      const urlObj = new URL(url);
      if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
        throw new BadRequestException('URL must use HTTP or HTTPS protocol');
      }
    } catch {
      throw new BadRequestException('Invalid URL format provided');
    }
  }

  /**
   * Generate a unique crawl ID for tracking
   */
  private generateCrawlId(): string {
    return `playwright_crawl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}
