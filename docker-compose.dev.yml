services:
  # Development configuration for NestJS Application
  nest-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: nest-crawler-app-dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      NODE_ENV: development
      PORT: 3000
      # Development-specific environment variables
      CRAWL_START_URLS: "[]"
      CRAWL_CONCURRENCY: 3
      LOG_LEVEL: debug
      # Playwright-specific environment variables
      PLAYWRIGHT_BROWSER_TYPE: chromium
      PLAYWRIGHT_HEADLESS: "true"
      PLAYWRIGHT_DEFAULT_TIMEOUT: 5000
      PLAYWRIGHT_MAX_CONCURRENCY: 3
      PLAYWRIGHT_SCREENSHOTS_ENABLED: "false"
      PLAYWRIGHT_BLOCK_IMAGES: "false"
      PLAYWRIGHT_BLOCK_CSS: "false"
    volumes:
      # Source code hot reload
      - .:/app
      - /app/node_modules
      # Persist crawled data in development
      - ./storage:/app/storage
      # Persist node_modules for faster rebuilds
      - node_modules_cache:/app/node_modules
    command: npm run start:dev
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for development (with exposed port for debugging)
  redis:
    image: redis:7-alpine
    container_name: nest-crawler-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    environment:
      REDIS_PASSWORD: ""  # No password for development
    command: redis-server --appendonly yes --save 60 1000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Optional: Redis Commander for Redis GUI management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: nest-crawler-redis-commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    depends_on:
      - redis
    restart: unless-stopped

  # Optional: Adminer for database management (if you add a database later)
  # adminer:
  #   image: adminer:latest
  #   container_name: nest-crawler-adminer
  #   ports:
  #     - "8080:8080"
  #   restart: unless-stopped

volumes:
  node_modules_cache:
    driver: local
  redis_dev_data:
    driver: local

networks:
  default:
    name: nest-crawler-dev-network
