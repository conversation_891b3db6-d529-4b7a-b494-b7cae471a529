import {
  IsUrl,
  <PERSON>NotEmpty,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsString,
  IsIn,
  Min,
  Max,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { BrowserType } from '../interfaces/playwright.interfaces';

export class ViewportDto {
  @IsNumber()
  @Min(100)
  @Max(3840)
  width: number;

  @IsNumber()
  @Min(100)
  @Max(2160)
  height: number;
}

export class ProxyDto {
  @IsString()
  @IsNotEmpty()
  server: string;

  @IsOptional()
  @IsString()
  username?: string;

  @IsOptional()
  @IsString()
  password?: string;
}

export class PlaywrightCrawlRequestDto {
  @IsNotEmpty()
  @IsUrl({}, { message: 'Please provide a valid URL' })
  url: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  maxConcurrency?: number = 3;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  maxPages?: number = 10;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  headless?: boolean = true;

  @IsOptional()
  @IsIn(['chromium', 'firefox', 'webkit'])
  browserType?: BrowserType = 'chromium';

  @IsOptional()
  @IsString()
  waitForSelector?: string;

  @IsOptional()
  @IsNumber()
  @Min(1000)
  @Max(60000)
  waitForTimeout?: number = 5000;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  screenshot?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  extractJavaScript?: boolean = false;

  @IsOptional()
  @ValidateNested()
  @Type(() => ViewportDto)
  viewport?: ViewportDto;

  @IsOptional()
  @IsString()
  userAgent?: string;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  blockImages?: boolean = false;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  blockCSS?: boolean = false;

  @IsOptional()
  @IsObject()
  extraHeaders?: Record<string, string>;

  @IsOptional()
  @ValidateNested()
  @Type(() => ProxyDto)
  proxy?: ProxyDto;
}
