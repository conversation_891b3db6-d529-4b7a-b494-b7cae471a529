/**
 * Browser types supported by Playwright
 */
export type BrowserType = 'chromium' | 'firefox' | 'webkit';

/**
 * Playwright crawler configuration options
 */
export interface PlaywrightCrawlOptions {
  /** Whether to run browser in headless mode */
  headless?: boolean;
  
  /** Browser type to use for crawling */
  browserType?: BrowserType;
  
  /** CSS selector to wait for before extracting data */
  waitForSelector?: string;
  
  /** Maximum time to wait for page load or selector (in milliseconds) */
  waitForTimeout?: number;
  
  /** Whether to take screenshots of pages */
  screenshot?: boolean;
  
  /** Whether to extract JavaScript-rendered content */
  extractJavaScript?: boolean;
  
  /** Custom viewport size */
  viewport?: {
    width: number;
    height: number;
  };
  
  /** User agent string to use */
  userAgent?: string;
  
  /** Whether to block images to speed up crawling */
  blockImages?: boolean;
  
  /** Whether to block CSS to speed up crawling */
  blockCSS?: boolean;
  
  /** Custom headers to send with requests */
  extraHeaders?: Record<string, string>;
  
  /** Proxy configuration */
  proxy?: {
    server: string;
    username?: string;
    password?: string;
  };
}

/**
 * Extended crawled data interface for Playwright results
 */
export interface PlaywrightCrawledData {
  /** URL of the crawled page */
  url: string;
  
  /** Page title */
  title: string;
  
  /** Timestamp when the page was crawled */
  crawledAt: Date;
  
  /** Meta description if available */
  metaDescription?: string;
  
  /** Extracted headings */
  headings?: string[];
  
  /** Screenshot path if taken */
  screenshotPath?: string;
  
  /** Page load time in milliseconds */
  loadTime?: number;
  
  /** HTTP status code */
  statusCode?: number;
  
  /** Custom extracted data */
  customData?: Record<string, any>;
}

/**
 * Result interface for Playwright crawling operations
 */
export interface PlaywrightCrawlResult {
  /** Array of crawled data */
  data: PlaywrightCrawledData[];
  
  /** Unique identifier for this crawl session */
  crawlId: string;
  
  /** Total number of pages crawled */
  totalPages: number;
  
  /** Array of screenshot file paths if screenshots were taken */
  screenshots?: string[];
  
  /** Total crawl duration in milliseconds */
  duration?: number;
  
  /** Browser type used for crawling */
  browserType?: BrowserType;
  
  /** Whether crawl was run in headless mode */
  headless?: boolean;
}

/**
 * Configuration for Playwright crawler service
 */
export interface PlaywrightServiceConfig {
  /** Default browser type */
  defaultBrowserType: BrowserType;
  
  /** Default headless mode */
  defaultHeadless: boolean;
  
  /** Default timeout for page operations */
  defaultTimeout: number;
  
  /** Default maximum concurrency */
  defaultMaxConcurrency: number;
  
  /** Default maximum pages per crawl */
  defaultMaxPages: number;
  
  /** Whether to enable screenshots by default */
  defaultScreenshots: boolean;
  
  /** Directory to store screenshots */
  screenshotDirectory: string;
  
  /** Whether to block images by default */
  defaultBlockImages: boolean;
  
  /** Whether to block CSS by default */
  defaultBlockCSS: boolean;
}

/**
 * Error types specific to Playwright crawling
 */
export enum PlaywrightCrawlErrorType {
  BROWSER_LAUNCH_FAILED = 'BROWSER_LAUNCH_FAILED',
  PAGE_LOAD_TIMEOUT = 'PAGE_LOAD_TIMEOUT',
  SELECTOR_NOT_FOUND = 'SELECTOR_NOT_FOUND',
  SCREENSHOT_FAILED = 'SCREENSHOT_FAILED',
  NAVIGATION_FAILED = 'NAVIGATION_FAILED',
  EXTRACTION_FAILED = 'EXTRACTION_FAILED',
}

/**
 * Custom error class for Playwright crawling operations
 */
export interface PlaywrightCrawlError {
  type: PlaywrightCrawlErrorType;
  message: string;
  url?: string;
  crawlId?: string;
  originalError?: Error;
}

/**
 * Statistics for Playwright crawling session
 */
export interface PlaywrightCrawlStats {
  /** Total pages attempted */
  pagesAttempted: number;
  
  /** Total pages successfully crawled */
  pagesSuccessful: number;
  
  /** Total pages that failed */
  pagesFailed: number;
  
  /** Average page load time */
  averageLoadTime: number;
  
  /** Total crawl duration */
  totalDuration: number;
  
  /** Browser type used */
  browserType: BrowserType;
  
  /** Whether run in headless mode */
  headless: boolean;
  
  /** Number of screenshots taken */
  screenshotsTaken: number;
}
